# SmaTrendFollower Configuration Guide

This document provides comprehensive configuration information for the SmaTrendFollower trading system.

## Environment Variables

All configuration is managed through environment variables loaded from a `.env` file. Copy `.env.example` to `.env` and update with your credentials.

### Required Configuration

#### Alpaca Markets API (Required)
```bash
# Alpaca API credentials - Get from https://alpaca.markets/
APCA_API_KEY_ID=your_alpaca_api_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_key_here
APCA_API_ENV=paper  # or "live" for real trading

# Optional: Custom API base URL (defaults to paper-api.alpaca.markets for paper, api.alpaca.markets for live)
# APCA_API_BASE_URL=https://paper-api.alpaca.markets
```

**Setup Instructions:**
1. Sign up at [Alpaca Markets](https://alpaca.markets/)
2. Create API keys in your dashboard
3. For testing, use paper trading environment
4. For live trading, ensure account has sufficient equity and trading permissions

#### Polygon.io API (Required for Enhanced Features)
```bash
# Polygon API key - Get from https://polygon.io/
POLY_API_KEY=your_polygon_api_key_here
```

**Setup Instructions:**
1. Sign up at [Polygon.io](https://polygon.io/)
2. Subscribe to required data plans:
   - **Indices Starter**: For SPX, VIX, DJI, NDX data
   - **Options Starter**: For options Greeks and implied volatility
3. Generate API key from dashboard

### Optional Configuration

#### Discord Notifications
```bash
# Discord bot for trade notifications
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_CHANNEL_ID=1385057459814797383  # Your Discord channel ID
```

**Setup Instructions:**
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create new application and bot
3. Copy bot token
4. Invite bot to your server with "Send Messages" permission
5. Get channel ID by right-clicking channel and "Copy ID"

#### Bot Settings
```bash
# Timezone for market hours calculation
BOT_TIMEZONE=America/New_York

# Logging level (Debug, Information, Warning, Error, Critical)
LOG_LEVEL=Information
```

### Strategy Configuration

#### Universe and Signal Generation
```bash
# Default universe size for screening (default: 500)
UNIVERSE_SIZE=500

# Top N symbols to trade after ranking (default: 10)
TOP_N_SYMBOLS=10

# Minimum average daily volume for universe filtering (default: 20M)
MIN_ADV=********
```

#### Volatility and Risk Management
```bash
# VIX threshold for position size throttling (default: 25.0)
VIX_THRESHOLD=25.0

# Maximum position size as percentage of account (default: 0.10 = 10%)
MAX_POSITION_SIZE_PERCENT=0.10

# Maximum daily loss threshold in dollars (default: 5000)
MAX_DAILY_LOSS=5000

# Minimum account equity required for trading (default: 25000 for PDT)
MIN_ACCOUNT_EQUITY=25000
```

#### Options Overlay Settings
```bash
# Enable options overlay strategies (default: true)
ENABLE_OPTIONS_OVERLAY=true

# Enable protective puts during high VIX (default: true)
ENABLE_PROTECTIVE_PUTS=true

# Enable covered calls on 100+ share positions (default: true)
ENABLE_COVERED_CALLS=true

# Enable delta-efficient exposure using deep ITM calls (default: false)
ENABLE_DELTA_EFFICIENT=false

# Minimum portfolio value for options strategies (default: 100000)
MIN_OPTIONS_PORTFOLIO_VALUE=100000
```

### Safety Configuration

#### Trading Safety Guards
```bash
# Maximum number of positions (default: 20)
MAX_POSITIONS=20

# Maximum single trade value in dollars (default: 2000)
MAX_SINGLE_TRADE_VALUE=2000

# Require confirmation for live trading (default: true)
REQUIRE_CONFIRMATION=true

# Enable dry run mode - no actual trades (default: false)
DRY_RUN_MODE=false

# Allowed trading environment: Paper, Live, or Both (default: Paper)
ALLOWED_ENVIRONMENT=Paper
```

#### Rate Limiting
```bash
# Alpaca API rate limit (requests per minute, default: 200)
ALPACA_RATE_LIMIT=200

# Polygon API rate limit (requests per second, default: 5)
POLYGON_RATE_LIMIT=5

# Rate limit retry attempts (default: 3)
RATE_LIMIT_RETRIES=3
```

### Cache Configuration

#### SQLite Cache Settings
```bash
# Stock bar cache retention in days (default: 365)
STOCK_CACHE_RETENTION_DAYS=365

# Index cache retention in days (default: 365)
INDEX_CACHE_RETENTION_DAYS=365

# Enable cache compression for old data (default: true)
ENABLE_CACHE_COMPRESSION=true

# Cache compression threshold in days (default: 30)
CACHE_COMPRESSION_THRESHOLD_DAYS=30

# Enable cache warming on startup (default: false)
ENABLE_CACHE_WARMING=false
```

## Configuration Validation

The system automatically validates configuration on startup:

### Required Validations
- Alpaca API credentials are present and valid
- Polygon API key is present (if enhanced features enabled)
- Numeric values are within valid ranges
- Environment settings match allowed values

### Warning Validations
- Discord configuration (optional but recommended)
- Cache settings optimization
- Safety parameter recommendations

### Error Conditions
- Missing required API credentials
- Invalid numeric values
- Conflicting safety settings
- Insufficient account permissions

## Environment-Specific Configuration

### Development Environment
```bash
# Use paper trading for development
APCA_API_ENV=paper
DRY_RUN_MODE=true
LOG_LEVEL=Debug
REQUIRE_CONFIRMATION=false
```

### Testing Environment
```bash
# Minimal configuration for unit tests
APCA_API_ENV=paper
ENABLE_OPTIONS_OVERLAY=false
DRY_RUN_MODE=true
LOG_LEVEL=Warning
```

### Production Environment
```bash
# Live trading configuration
APCA_API_ENV=live
DRY_RUN_MODE=false
LOG_LEVEL=Information
REQUIRE_CONFIRMATION=true
ALLOWED_ENVIRONMENT=Live
MAX_DAILY_LOSS=1000
```

## Configuration File Locations

### Primary Configuration
- **`.env`**: Main environment variables file (not committed to git)
- **`.env.example`**: Template file with example values (committed to git)

### Universe Configuration
- **`universe.csv`**: Symbol universe file (one symbol per line)
- **Default Universe**: Used if universe.csv not found

### Cache Databases
- **`stock_cache.db`**: SQLite database for stock/ETF bar cache
- **`index_cache.db`**: SQLite database for index data cache

### Log Files
- **`logs/sma-trend-follower-YYYY-MM-DD.log`**: Daily rolling log files
- **Console Output**: Real-time logging to console

## Security Best Practices

### API Key Management
- Never commit real API keys to version control
- Use environment variables for all sensitive data
- Rotate API keys regularly
- Use paper trading for development and testing

### File Permissions
- Restrict `.env` file permissions (600 on Unix systems)
- Secure log file access
- Protect SQLite database files

### Network Security
- Use HTTPS for all API communications
- Validate SSL certificates
- Consider VPN for production deployments

## Troubleshooting Configuration

### Common Issues

#### API Connection Failures
```bash
# Check API credentials
dotnet run --project SmaTrendFollower.Console -- validate-config

# Test Alpaca connection
dotnet run --project SmaTrendFollower.Console -- test-alpaca

# Test Polygon connection
dotnet run --project SmaTrendFollower.Console -- test-polygon
```

#### Cache Issues
```bash
# Clear cache databases
rm *.db

# Rebuild cache
dotnet run --project SmaTrendFollower.Console -- cache-maintenance 0
```

#### Discord Notification Issues
```bash
# Test Discord configuration
dotnet run --project SmaTrendFollower.Console -- test-discord
```

### Configuration Validation Script
```bash
# Validate all configuration
./validate-environment.ps1

# Simple validation
./validate-environment-simple.ps1
```

## Advanced Configuration

### Custom Universe File
Create `universe.csv` with one symbol per line:
```
SPY
QQQ
AAPL
MSFT
NVDA
TSLA
AMZN
GOOGL
META
NFLX
```

### Custom Logging Configuration
The system uses Serilog with configurable output:
- Console logging with colored output
- File logging with daily rolling
- Structured JSON logging for external systems

### Performance Tuning
- Adjust cache retention for storage optimization
- Configure rate limits based on API subscription
- Optimize universe size for processing speed
- Enable cache warming for faster startup

## Migration Guide

### From Previous Versions
1. Update `.env` file with new variables
2. Run cache maintenance to update database schema
3. Validate configuration with new validation script
4. Test with paper trading before live deployment

### Configuration Schema Changes
- Monitor release notes for configuration changes
- Use configuration validation to identify issues
- Update environment files accordingly
