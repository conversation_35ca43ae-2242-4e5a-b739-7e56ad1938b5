# SmaTrendFollower Architecture Documentation

This document provides a comprehensive overview of the SmaTrendFollower system architecture, including service dependencies, data flow, and design patterns.

## System Overview

SmaTrendFollower is a sophisticated .NET 8 trading system that implements an SMA-following momentum strategy with advanced options overlay capabilities. The system follows clean architecture principles with dependency injection, comprehensive testing, and robust error handling.

### Key Architectural Principles

- **Single Responsibility**: Each service has a focused, well-defined purpose
- **Dependency Injection**: All services use constructor injection for testability
- **Async/Await**: All I/O operations are asynchronous for performance
- **Interface Segregation**: Small, focused interfaces for better maintainability
- **Error Handling**: Comprehensive exception handling with structured logging
- **Rate Limiting**: Built-in rate limiting for all external API calls

## Service Architecture

### Core Trading Services

#### Trading Orchestration
- **EnhancedTradingService**: Main orchestrator that coordinates the entire trading cycle
- **TradingService**: Base trading service for standard equity strategies
- **MarketSessionGuard**: Validates trading is allowed based on market hours and weekdays

#### Signal Generation & Analysis
- **EnhancedSignalGenerator**: Advanced signal generation with universe screening
- **SignalGenerator**: Base signal generator for SMA-based momentum signals
- **VolatilityManager**: VIX-based volatility regime analysis and position adjustments

#### Risk Management
- **RiskManager**: Position sizing with 10bps per $100k cap and VIX adjustments
- **PortfolioGate**: SPY SMA200 trend filter for market regime validation
- **TradingSafetyGuard**: Comprehensive safety validation for trades and cycles
- **SafetyConfigurationService**: Configurable safety parameters and risk limits

#### Trade Execution
- **SafeTradeExecutor**: Safety wrapper around trade execution with validation
- **TradeExecutor**: Core trade execution with Limit-on-Open and stop-loss orders
- **StopManager**: Manages trailing stops and position protection

#### Options Strategies
- **OptionsStrategyManager**: Advanced options overlay strategies
  - Protective puts during high VIX regimes
  - Covered calls on 100+ share positions
  - Delta-efficient exposure using deep ITM calls

### Data & Infrastructure Services

#### Market Data Integration
- **MarketDataService**: Unified interface combining Alpaca and Polygon data sources
  - Account data, positions, and live fills from Alpaca
  - Historical bars with automatic fallback between providers
  - Index data (SPX, VIX, DJI, NDX) from Polygon
  - Options data with Greeks, IV, and OI from Polygon

#### Real-Time Streaming
- **StreamingDataService**: WebSocket connections for real-time market data
  - Alpaca WebSocket for equity quotes, bars, and trade updates
  - Polygon WebSocket for index updates and VIX spikes
  - Event-driven architecture with comprehensive error recovery

#### Client Factories
- **AlpacaClientFactory**: Creates and manages Alpaca API clients
  - Trading client for order management
  - Data client for historical data
  - Streaming client for real-time data
  - Rate limiting helper (200 requests/minute)

- **PolygonClientFactory**: Creates and manages Polygon HTTP clients
  - HTTP client with proper authentication
  - API key injection for requests
  - Rate limiting helper (5 requests/second)

### Caching & Performance Services

#### SQLite Caching System
- **StockBarCacheService**: Caches stock/ETF historical data
  - 1-year retention with daily/minute timeframes
  - Compression for bars older than 30 days
  - Bulk operations for performance optimization

- **IndexCacheService**: Caches index data (SPX, VIX, etc.)
  - Nightly refresh capability
  - Optimized for index symbols
  - Differential data updates

#### Cache Management
- **CacheManagementService**: Coordinates cache operations and maintenance
- **CacheMetricsService**: Monitors cache performance and hit ratios
- **CacheWarmingService**: Proactive cache loading for improved performance
- **CacheCompressionService**: Compresses old data to save storage space

### Notification & Communication

#### Discord Integration
- **DiscordNotificationService**: Real-time trading notifications
  - Trade execution alerts with P&L
  - Daily portfolio snapshots
  - VIX spike alerts with recommended actions
  - Options strategy notifications

### Utility Services

#### Infrastructure Support
- **SystemTimeProvider**: Time abstraction for testing support
- **FileUniverseProvider**: Symbol universe management from CSV files
- **RateLimitPolicyFactory**: Exponential backoff policies for API rate limiting

## Data Flow Architecture

### Trading Cycle Flow

1. **Initialization**: Program.cs → MarketSessionGuard → TradingService
2. **Safety Validation**: TradingSafetyGuard validates trading cycle parameters
3. **Stop Management**: Update trailing stops for existing positions
4. **Market Regime Check**: PortfolioGate validates SPY > SMA200
5. **Volatility Analysis**: VolatilityManager analyzes VIX regime
6. **Signal Generation**: SignalGenerator screens universe and generates signals
7. **Position Sizing**: RiskManager calculates quantities with VIX adjustments
8. **Trade Execution**: TradeExecutor places orders with stop-losses
9. **Options Overlay**: OptionsStrategyManager evaluates and executes strategies
10. **Notifications**: DiscordNotificationService sends updates

### Data Source Integration

#### Alpaca Markets (Primary Trading Data)
- **Account Management**: Real-time account info, equity, buying power
- **Position Tracking**: Current holdings with P&L
- **Order Management**: Trade execution and order status
- **Historical Data**: Daily and minute bars for equities/ETFs
- **Real-time Streaming**: Live quotes, bars, and trade updates

#### Polygon.io (Enhanced Market Data)
- **Index Data**: SPX, VIX, DJI, NDX values and historical bars
- **Options Data**: Greeks, implied volatility, open interest
- **VIX Analysis**: Term structure and volatility regime data
- **Fallback Provider**: Alternative source when Alpaca is throttled

### Caching Strategy

#### Cache Hierarchy
1. **Memory Cache**: In-memory caching for frequently accessed data
2. **SQLite Cache**: Persistent caching with compression and optimization
3. **API Fallback**: External API calls when cache misses occur

#### Cache Optimization
- **Compression**: Automatic compression for data older than 30 days
- **Bulk Operations**: Batch inserts and updates for performance
- **Connection Pooling**: Efficient database connection management
- **Cache Warming**: Proactive loading of commonly accessed data

## Design Patterns

### Dependency Injection Pattern
All services use constructor injection with interface-based dependencies:

```csharp
public class TradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    // ... other dependencies
    
    public TradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _portfolioGate = portfolioGate;
    }
}
```

### Factory Pattern
Client factories abstract the creation and configuration of external API clients:

```csharp
public interface IAlpacaClientFactory
{
    IAlpacaTradingClient CreateTradingClient();
    IAlpacaDataClient CreateDataClient();
    IAlpacaStreamingClient CreateStreamingClient();
}
```

### Strategy Pattern
Options strategies are implemented using the strategy pattern:

```csharp
public interface IOptionsStrategyManager
{
    Task<ProtectivePutResult> EvaluateProtectivePutAsync(...);
    Task<CoveredCallResult> EvaluateCoveredCallAsync(...);
    Task<DeltaEfficientResult> EvaluateDeltaEfficientExposureAsync(...);
}
```

### Decorator Pattern
Safety wrapper decorates the trade executor with additional validation:

```csharp
public class SafeTradeExecutor : ITradeExecutor
{
    private readonly TradeExecutor _innerExecutor;
    private readonly ITradingSafetyGuard _safetyGuard;
    
    public async Task ExecuteTradeAsync(TradingSignal signal, decimal quantity)
    {
        var validation = await _safetyGuard.ValidateTradeAsync(signal, quantity);
        if (!validation.IsAllowed) return;
        
        await _innerExecutor.ExecuteTradeAsync(signal, quantity);
    }
}
```

### Observer Pattern
Streaming data service uses events for real-time data distribution:

```csharp
public interface IStreamingDataService
{
    event EventHandler<QuoteEventArgs>? QuoteReceived;
    event EventHandler<BarEventArgs>? BarReceived;
    event EventHandler<VixSpikeEventArgs>? VixSpikeDetected;
}
```

## Error Handling & Resilience

### Exception Handling Strategy
- **Service Level**: Each service handles its own exceptions with logging
- **API Level**: Rate limiting and retry policies for external APIs
- **Application Level**: Global exception handling in Program.cs

### Rate Limiting & Retry Policies
- **Exponential Backoff**: Automatic retry with increasing delays
- **Circuit Breaker**: Temporary failure protection for external services
- **Fallback Mechanisms**: Alternative data sources when primary fails

### Connection Recovery
- **WebSocket Reconnection**: Automatic reconnection for streaming data
- **Database Resilience**: Connection pooling and retry logic
- **API Failover**: Automatic fallback between Alpaca and Polygon

## Testing Architecture

### Unit Testing Strategy
- **Service Isolation**: Each service tested in isolation using Moq
- **Interface Mocking**: All dependencies mocked via interfaces
- **Comprehensive Coverage**: 230+ tests covering all major functionality

### Integration Testing
- **API Integration**: Tests requiring real API credentials (skipped in CI)
- **Database Integration**: Tests using in-memory SQLite databases
- **End-to-End**: Complete trading cycle validation

### Test Categories
- **Unit Tests**: Individual service component testing
- **Integration Tests**: Service interaction testing
- **Performance Tests**: Cache and API performance validation
- **Safety Tests**: Trading safety guard validation

## Security Considerations

### API Security
- **Environment Variables**: All credentials stored in environment variables
- **No Hardcoded Keys**: No API keys committed to source control
- **HTTPS Only**: All API communications use HTTPS

### Data Protection
- **Local Storage**: SQLite databases stored locally with appropriate permissions
- **Log Sanitization**: No sensitive data logged
- **Memory Management**: Proper disposal of sensitive objects

### Trading Safety
- **Paper Trading**: Default to paper trading environment
- **Safety Guards**: Multiple layers of safety validation
- **Position Limits**: Configurable maximum position sizes
- **Loss Limits**: Daily loss thresholds with automatic halt

## Performance Optimization

### Caching Strategy
- **Hit Ratio Optimization**: Target >90% cache hit ratio for historical data
- **Compression**: Reduce storage requirements for old data
- **Bulk Operations**: Minimize database round trips

### API Efficiency
- **Rate Limiting**: Respect API limits to avoid throttling
- **Batch Requests**: Group multiple symbol requests when possible
- **Intelligent Fallback**: Use cached data when APIs are unavailable

### Memory Management
- **Disposal Pattern**: Proper disposal of HTTP clients and database connections
- **Streaming Optimization**: Efficient handling of real-time data streams
- **Resource Pooling**: Connection pooling for database and HTTP clients

## Deployment Architecture

### Single-Shot Execution Model
- **Manual Execution**: Run via `dotnet run` for immediate execution
- **Scheduled Execution**: External scheduler (cron, Task Scheduler)
- **Containerized**: Docker support for consistent deployment
- **Cloud Ready**: Compatible with serverless platforms

### Configuration Management
- **Environment Variables**: All configuration via .env files
- **Validation**: Startup configuration validation
- **Flexibility**: Support for development, testing, and production environments

### Monitoring & Observability
- **Structured Logging**: JSON-formatted logs for external systems
- **Performance Metrics**: Cache hit ratios and API call statistics
- **Discord Notifications**: Real-time trading alerts and status updates
- **Error Tracking**: Comprehensive error logging and alerting
