# SmaTrendFollower Service Interfaces Documentation

This document provides comprehensive documentation for all service interfaces in the SmaTrendFollower system.

## Core Trading Services

### ISignalGenerator
**Purpose**: Generates trading signals through universe screening and technical analysis.

```csharp
public interface ISignalGenerator
{
    Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10);
}
```

**Implementation**: `SignalGenerator` / `EnhancedSignalGenerator`
- Screens SPY + top-500 tickers from universe
- Applies SMA50/SMA200 trend filters
- Filters by volatility throttle (ATR/close < 3%)
- Ranks by 6-month total return
- Returns top N symbols for trading

### IRiskManager
**Purpose**: Calculates position sizes based on account equity and volatility.

```csharp
public interface IRiskManager
{
    Task<decimal> CalculateQuantityAsync(TradingSignal signal);
}
```

**Implementation**: `RiskManager`
- Implements 10bps per $100k equity cap
- Applies VIX-based position adjustments
- Uses ATR-based position sizing: qty = riskDollars / (ATR × price)
- Ensures fractional share support

### IPortfolioGate
**Purpose**: Determines if trading should occur based on market conditions.

```csharp
public interface IPortfolioGate
{
    Task<bool> ShouldTradeAsync();
}
```

**Implementation**: `PortfolioGate`
- Checks SPY close > SPY SMA200 (market trend filter)
- Returns false if broad market is in downtrend
- Prevents trading during unfavorable market regimes

### ITradeExecutor
**Purpose**: Executes trades with proper order management and stop-loss placement.

```csharp
public interface ITradeExecutor
{
    Task ExecuteTradeAsync(TradingSignal signal, decimal quantity);
}
```

**Implementation**: `TradeExecutor` / `SafeTradeExecutor`
- Cancels existing orders for symbol
- Places Limit-on-Open orders at lastClose × 1.002
- Sets GTC stop-loss at entry - 2×ATR
- Includes safety wrapper for additional validation

### ITradingService
**Purpose**: Orchestrates the complete trading cycle.

```csharp
public interface ITradingService
{
    Task ExecuteCycleAsync(CancellationToken cancellationToken = default);
}
```

**Implementation**: `TradingService` / `EnhancedTradingService`
- Coordinates all trading services
- Manages trading cycle flow
- Handles errors and logging
- Enhanced version includes options overlay strategies

## Enhanced Strategy Services

### IVolatilityManager
**Purpose**: Analyzes VIX-based volatility regimes and adjusts position sizing.

```csharp
public interface IVolatilityManager
{
    Task<VolatilityRegime> GetCurrentRegimeAsync();
    Task<decimal> GetVixPositionAdjustmentAsync();
    Task<bool> IsVixSpikeDetectedAsync(decimal threshold = 25.0m);
    Task<decimal> GetIvAdjustedStopAsync(string symbol, decimal entryPrice, decimal baseAtr);
}
```

**Implementation**: `VolatilityManager`
- Categorizes market into Low/Medium/High VIX regimes
- Provides position size adjustments based on volatility
- Detects VIX spikes for defensive positioning
- Calculates IV-adjusted stop-loss levels

### IOptionsStrategyManager
**Purpose**: Manages options overlay strategies for enhanced returns and protection.

```csharp
public interface IOptionsStrategyManager
{
    Task<ProtectivePutResult> EvaluateProtectivePutAsync(string symbol, decimal portfolioValue, decimal currentPrice);
    Task<CoveredCallResult> EvaluateCoveredCallAsync(string symbol, decimal sharesOwned, decimal currentPrice);
    Task<DeltaEfficientResult> EvaluateDeltaEfficientExposureAsync(string symbol, decimal targetExposure, decimal currentPrice);
    Task ManageExistingOptionsAsync();
    Task ManageExpirationRiskAsync();
}
```

**Implementation**: `OptionsStrategyManager`
- Evaluates protective put opportunities during high VIX
- Manages covered call income strategies on 100+ share positions
- Implements delta-efficient exposure using deep ITM calls
- Monitors existing options positions and expiration risk

### IDiscordNotificationService
**Purpose**: Sends real-time trading notifications via Discord.

```csharp
public interface IDiscordNotificationService
{
    Task SendTradeNotificationAsync(string symbol, string action, decimal quantity, decimal price, decimal pnl);
    Task SendPortfolioSnapshotAsync(decimal totalEquity, decimal dayPnl, decimal totalPnl, int positionCount);
    Task SendVixSpikeAlertAsync(decimal currentVix, decimal threshold, string action);
    Task SendOptionsNotificationAsync(string strategy, string symbol, string details);
}
```

**Implementation**: `DiscordNotificationService`
- Sends trade execution notifications
- Provides daily portfolio snapshots
- Alerts on VIX spikes and volatility events
- Notifies on options strategy executions

## Data & Infrastructure Services

### IMarketDataService
**Purpose**: Unified interface for market data from Alpaca and Polygon sources.

```csharp
public interface IMarketDataService
{
    // Account & Portfolio Data
    Task<IAccount> GetAccountAsync();
    Task<IReadOnlyList<IPosition>> GetPositionsAsync();
    Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100);
    
    // Stock Data
    Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    Task<IPage<IBar>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);
    
    // Index Data
    Task<decimal> GetIndexValueAsync(string indexSymbol);
    Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate);
    
    // Options Data
    Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null);
    Task<IEnumerable<VixTermData>> GetVixTermStructureAsync();
    Task<IEnumerable<OptionQuote>> GetOptionsQuotesAsync(IEnumerable<string> optionSymbols);
    Task<IEnumerable<OptionData>> GetProtectivePutOptionsAsync(string underlyingSymbol, int daysToExpiration = 30);
    Task<IEnumerable<OptionData>> GetCoveredCallOptionsAsync(string underlyingSymbol, decimal currentPrice, int daysToExpiration = 7);
    
    // Universe & Screening
    Task<IEnumerable<string>> GetUniverseWithAdvFilterAsync(decimal minAdv = 20_000_000m);
}
```

**Implementation**: `MarketDataService`
- Combines Alpaca and Polygon data sources
- Provides automatic fallback between data providers
- Implements intelligent caching for performance
- Handles rate limiting and error recovery

### IStreamingDataService
**Purpose**: Real-time market data streaming via WebSocket connections.

```csharp
public interface IStreamingDataService
{
    // Events
    event EventHandler<QuoteEventArgs>? QuoteReceived;
    event EventHandler<BarEventArgs>? BarReceived;
    event EventHandler<TradeUpdateEventArgs>? TradeUpdated;
    event EventHandler<VixSpikeEventArgs>? VixSpikeDetected;
    event EventHandler<OptionsQuoteEventArgs>? OptionsQuoteReceived;
    
    // Connection Management
    Task ConnectAlpacaStreamAsync();
    Task ConnectPolygonStreamAsync();
    Task DisconnectAllAsync();
    
    // Subscriptions
    Task SubscribeToQuotesAsync(IEnumerable<string> symbols);
    Task SubscribeToBarsAsync(IEnumerable<string> symbols);
    Task SubscribeToSecondBarsAsync(IEnumerable<string> symbols);
    Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols);
    Task SubscribeToTradeUpdatesAsync();
    Task SubscribeToVixUpdatesAsync();
    Task SubscribeToOptionsQuotesAsync(IEnumerable<string> optionSymbols);
    Task UnsubscribeAllAsync();
    
    // Status
    StreamingConnectionStatus ConnectionStatus { get; }
}
```

**Implementation**: `StreamingDataService`
- Manages dual WebSocket connections (Alpaca + Polygon)
- Provides real-time quotes, bars, and trade updates
- Detects VIX spikes and volatility events
- Handles connection recovery and error management

## Infrastructure Services

### IAlpacaClientFactory
**Purpose**: Creates and manages Alpaca API clients with rate limiting.

```csharp
public interface IAlpacaClientFactory
{
    IAlpacaTradingClient CreateTradingClient();
    IAlpacaDataClient CreateDataClient();
    IAlpacaStreamingClient CreateStreamingClient();
    AlpacaRateLimitHelper GetRateLimitHelper();
}
```

**Implementation**: `AlpacaClientFactory`
- Creates properly configured Alpaca clients
- Manages API credentials and environment settings
- Provides rate limiting helpers for API calls
- Handles client lifecycle and disposal

### IPolygonClientFactory
**Purpose**: Creates and manages Polygon HTTP clients with rate limiting.

```csharp
public interface IPolygonClientFactory
{
    HttpClient CreateClient();
    string AddApiKeyToUrl(string url);
    PolygonRateLimitHelper GetRateLimitHelper();
}
```

**Implementation**: `PolygonClientFactory`
- Creates HTTP clients for Polygon API
- Manages API key injection into requests
- Provides rate limiting for 5 requests/second limit
- Handles authentication and error recovery

## Caching Services

### IStockBarCacheService
**Purpose**: SQLite-based caching for stock/ETF historical data.

```csharp
public interface IStockBarCacheService
{
    Task<IPage<IBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate);
    Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars);
    Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame);
    Task<bool> HasCachedDataAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate);
    Task CleanupOldDataAsync(int retainDays = 365);
}
```

**Implementation**: `StockBarCacheService`
- Provides 1-year retention with daily/minute timeframes
- Implements compression for bars older than 30 days
- Supports bulk operations for performance
- Tracks cache metadata and statistics

### IIndexCacheService
**Purpose**: SQLite-based caching for index data (SPX, VIX, etc.).

```csharp
public interface IIndexCacheService
{
    Task<IEnumerable<IndexBar>> GetCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    Task CacheBarsAsync(string symbol, IEnumerable<IndexBar> bars);
    Task<DateTime?> GetLatestCachedDateAsync(string symbol);
    Task<bool> HasCachedDataAsync(string symbol, DateTime startDate, DateTime endDate);
    Task CleanupOldDataAsync(int retainDays = 365);
}
```

**Implementation**: `IndexCacheService`
- Caches index data with nightly refresh capability
- Optimized for index symbols (I:SPX, I:VIX, etc.)
- Provides differential data updates
- Supports cache warming and preloading

## Safety & Risk Services

### ITradingSafetyGuard
**Purpose**: Comprehensive safety validation for trading operations.

```csharp
public interface ITradingSafetyGuard
{
    Task<SafetyCheckResult> ValidateTradeAsync(TradingSignal signal, decimal quantity);
    Task<SafetyCheckResult> ValidateTradingCycleAsync();
    SafetyConfiguration GetConfiguration();
    void UpdateConfiguration(SafetyConfiguration config);
}
```

**Implementation**: `TradingSafetyGuard`
- Validates individual trades before execution
- Performs trading cycle safety checks
- Enforces position limits and risk controls
- Provides configurable safety parameters

### ISafetyConfigurationService
**Purpose**: Manages safety configuration and risk parameters.

```csharp
public interface ISafetyConfigurationService
{
    SafetyConfiguration GetConfiguration();
    void UpdateConfiguration(SafetyConfiguration config);
    bool ValidateConfiguration(SafetyConfiguration config);
}
```

**Implementation**: `SafetyConfigurationService`
- Loads safety configuration from environment variables
- Validates configuration parameters
- Provides default safety settings
- Supports runtime configuration updates

## Utility Services

### IMarketSessionGuard
**Purpose**: Validates if trading is allowed based on market hours.

```csharp
public interface IMarketSessionGuard
{
    Task<bool> IsMarketOpenAsync();
    Task<bool> ShouldTradeAsync();
}
```

**Implementation**: `MarketSessionGuard`
- Checks if current time is during market hours
- Validates weekday trading (no weekends)
- Considers market holidays and early closes
- Provides timezone-aware market session detection

### ITimeProvider
**Purpose**: Abstraction for time operations to support testing.

```csharp
public interface ITimeProvider
{
    DateTime UtcNow { get; }
    DateTime Now { get; }
    DateTimeOffset OffsetUtcNow { get; }
}
```

**Implementation**: `SystemTimeProvider`
- Provides current time for production use
- Enables time mocking in unit tests
- Supports timezone-aware operations
- Ensures consistent time handling across services

### IUniverseProvider
**Purpose**: Provides symbol universe for screening and trading.

```csharp
public interface IUniverseProvider
{
    Task<IEnumerable<string>> GetSymbolsAsync();
}
```

**Implementation**: `FileUniverseProvider`
- Loads symbols from universe.csv file
- Provides default universe if file not found
- Supports dynamic universe updates
- Validates symbol format and availability
