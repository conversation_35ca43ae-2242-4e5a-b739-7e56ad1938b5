using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using DotNetEnv;
using Serilog;

namespace SmaTrendFollower.Console;

/// <summary>
/// Simple test program to verify Discord notification service
/// </summary>
public static class TestDiscordService
{
    public static async Task<int> Main(string[] args)
    {
        // Load environment variables
        var envPath = ".env";
        if (File.Exists(envPath))
        {
            Env.Load(envPath);
        }

        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            using IHost host = Host.CreateDefaultBuilder(args)
                .UseSerilog()
                .ConfigureServices(services =>
                {
                    services.AddHttpClient();
                    services.AddScoped<IDiscordNotificationService, DiscordNotificationService>();
                })
                .Build();

            using var scope = host.Services.CreateScope();
            var discordService = scope.ServiceProvider.GetRequiredService<IDiscordNotificationService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

            logger.LogInformation("Testing Discord notification service...");

            // Test portfolio snapshot (this is what would be sent as a daily report)
            await discordService.SendPortfolioSnapshotAsync(
                totalEquity: 125000m,
                dayPnl: 1250m,
                totalPnl: 5000m,
                positionCount: 8
            );

            logger.LogInformation("Portfolio snapshot test completed");

            // Test trade notification
            await discordService.SendTradeNotificationAsync(
                symbol: "SPY",
                action: "BUY",
                quantity: 10m,
                price: 450.25m,
                pnl: 0m
            );

            logger.LogInformation("Trade notification test completed");

            // Test VIX spike alert
            await discordService.SendVixSpikeAlertAsync(
                currentVix: 28.5m,
                threshold: 25.0m,
                action: "Reducing position sizes"
            );

            logger.LogInformation("VIX spike alert test completed");

            logger.LogInformation("All Discord tests completed successfully!");
            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Discord test failed");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}
