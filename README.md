# SmaTrendFollower

A comprehensive .NET 8 console application that implements an advanced SMA-following momentum trading strategy with options overlay capabilities, real-time streaming data, and sophisticated risk management.

## 🚀 Features

### Core Trading Strategy
- **Single-Shot Execution**: Manual one-shot execution flow with market session guard
- **Universe Screening**: Screens SPY + top-500 tickers with SMA50/SMA200 filtering
- **Portfolio Gate**: SPY SMA200 check to determine if trading should occur
- **Risk Management**: 10bps per $100k equity cap with VIX-adjusted position sizing
- **Trade Execution**: Limit-on-Open pattern with 2×ATR stop-loss orders

### Enhanced Features
- **Options Overlay Strategies**: Protective puts, covered calls, delta-efficient exposure
- **VIX-Based Risk Management**: Dynamic position sizing based on volatility regime
- **Real-Time Streaming**: Live market data via Alpaca and Polygon WebSocket connections
- **SQLite Caching**: Intelligent bar caching with compression and performance optimization
- **Discord Notifications**: Real-time trade alerts and portfolio updates
- **Safety Guards**: Comprehensive trading safety validation and risk controls

### Technical Infrastructure
- **Comprehensive Logging**: Uses Serilog for console and file logging with daily rolling logs
- **Dependency Injection**: Clean architecture with scoped services for each trading cycle
- **Comprehensive Testing**: 230+ unit and integration tests with xUnit, FluentAssertions, and Moq
- **Rate Limiting**: Built-in rate limiting for Alpaca and Polygon APIs with exponential backoff
- **Data Integration**: Unified Alpaca + Polygon data sources with automatic fallback

## 🏗️ Architecture

The application follows a clean, modular architecture with dependency injection and comprehensive service separation:

### Core Trading Services
- **MarketSessionGuard**: Validates trading is allowed (weekdays only)
- **TradingService / EnhancedTradingService**: Orchestrates the complete trading cycle with options overlay
- **SignalGenerator / EnhancedSignalGenerator**: Universe screening with SPY + top-500 tickers, SMA filtering
- **PortfolioGate**: SPY SMA200 check to gate trading decisions
- **RiskManager**: 10bps per $100k cap with VIX-adjusted position sizing
- **TradeExecutor / SafeTradeExecutor**: Limit-on-Open + 2×ATR stop-loss execution with safety wrapper
- **StopManager**: Manages trailing stops and position protection

### Enhanced Strategy Services
- **VolatilityManager**: VIX-based regime analysis and position size adjustments
- **OptionsStrategyManager**: Protective puts, covered calls, and delta-efficient exposure strategies
- **DiscordNotificationService**: Real-time trade alerts and portfolio updates via Discord

### Data & Infrastructure Services
- **AlpacaClientFactory**: Creates and manages Alpaca API clients with rate limiting
- **PolygonClientFactory**: Creates and manages Polygon HTTP clients with rate limiting
- **MarketDataService**: Unified interface combining Alpaca + Polygon data sources
  - Account data, positions, live fills from Alpaca
  - Historical daily/minute bars with automatic fallback
  - Index data (SPX, VIX, DJI, NDX) and options Greeks from Polygon
- **StreamingDataService**: Real-time websocket data streams
  - Live quotes and bars for equity symbols (Alpaca)
  - Trade execution updates and fills (Alpaca)
  - Index/volatility triggers and VIX spikes (Polygon)
- **UniverseProvider**: Provides symbol universe from file or defaults

### Caching & Performance Services
- **StockBarCacheService**: SQLite-based caching for stock/ETF historical data
- **IndexCacheService**: SQLite-based caching for index data (SPX, VIX, etc.)
- **CacheManagementService**: Cache maintenance, compression, and optimization
- **CacheMetricsService**: Performance monitoring and cache hit ratio tracking
- **CacheWarmingService**: Proactive cache warming for improved performance

### Safety & Risk Services
- **TradingSafetyGuard**: Comprehensive safety validation for trades and trading cycles
- **SafetyConfigurationService**: Configurable safety parameters and risk limits
- **RateLimitPolicyFactory**: Exponential backoff policies for API rate limiting

## 🛠️ Setup

### Prerequisites

- **.NET 8 SDK** - Download from [Microsoft .NET](https://dotnet.microsoft.com/download)
- **Alpaca Markets Account** - Sign up at [Alpaca Markets](https://alpaca.markets/)
  - Paper trading account (recommended for testing)
  - Live trading account (for production use)
  - API keys with trading permissions
- **Polygon.io API Key** - Sign up at [Polygon.io](https://polygon.io/)
  - Indices Starter subscription (for SPX, VIX data)
  - Options Starter subscription (for options Greeks and IV)
- **Discord Bot Token** (optional) - For trade notifications
  - Create application at [Discord Developer Portal](https://discord.com/developers/applications)

### Configuration

1. **Copy the environment template**:
   ```bash
   cp SmaTrendFollower.Console/.env.example SmaTrendFollower.Console/.env
   ```

2. **Update the `.env` file with your credentials**:
   ```bash
   # Alpaca credentials (required)
   APCA_API_KEY_ID=your_alpaca_api_key_here
   APCA_API_SECRET_KEY=your_alpaca_secret_key_here
   APCA_API_ENV=paper  # or live for real trading

   # Polygon credentials (required for enhanced features)
   POLY_API_KEY=your_polygon_api_key_here

   # Discord notifications (optional)
   DISCORD_BOT_TOKEN=your_discord_bot_token_here
   DISCORD_CHANNEL_ID=your_discord_channel_id_here

   # Strategy settings
   TOP_N_SYMBOLS=10
   VIX_THRESHOLD=25.0
   ENABLE_OPTIONS_OVERLAY=true
   ```

3. **Create universe file** (optional):
   ```bash
   # Create universe.csv with one symbol per line
   echo -e "SPY\nQQQ\nAAPL\nMSFT\nNVDA\nTSLA\nAMZN\nGOOGL\nMETA\nNFLX" > universe.csv
   ```

### Running the Application

```bash
# Build the solution
dotnet build SmaTrendFollower.sln

# Run the main trading application
dotnet run --project SmaTrendFollower.Console

# Run with cache maintenance (optional)
dotnet run --project SmaTrendFollower.Console -- cache-maintenance

# Run tests (230+ tests)
dotnet test SmaTrendFollower.sln

# Run tests with detailed output
dotnet test SmaTrendFollower.sln --verbosity normal

# Run only unit tests (skip integration tests)
dotnet test SmaTrendFollower.sln --filter "Category!=Integration"
```

### Command Line Options

The application supports several command-line modes:

- **Default**: `dotnet run` - Executes full trading cycle
- **Cache Maintenance**: `dotnet run -- cache-maintenance [days]` - Cleans old cache data
- **Account Check**: Use the `AccountChecker.cs` utility for account validation

## 📈 Trading Strategy

The bot implements a sophisticated SMA-following momentum strategy with options overlay capabilities:

### Signal Generation
- **Universe**: SPY + top-500 tickers from universe.csv or defaults
- **Primary Trend Filter**: close > SMA50 && close > SMA200 (broad uptrend requirement)
- **Volatility Throttle**: 14-day ATR / close < 3% (avoid whipsaw regimes)
- **Ranking**: Ranked by 6-month total return descending (momentum concentration)
- **Selection**: Top N symbols (default 10) for capital concentration in leaders

### Portfolio Gate & Market Regime
- **SPY SMA200 Check**: Only trade when SPY close > SPY SMA200 (market trend filter)
- **Market Session**: Only trade on weekdays (no weekend gaps)
- **VIX Regime Analysis**: Dynamic position sizing based on volatility environment
  - Low VIX (<20): Normal position sizes
  - Medium VIX (20-25): Reduced position sizes
  - High VIX (>25): Defensive positioning with protective puts

### Risk Management
- **Base Risk Capital**: min(account equity × 1%, $1000) - 10bps per $100k cap
- **VIX Adjustment**: Position sizes scaled by volatility regime
- **Position Sizing**: quantity = (riskDollars × vixAdjustment) / (ATR14 × price)
- **Max Position**: quantity ≤ riskDollars / price (hard limit)
- **Daily Loss Limits**: Configurable maximum daily loss thresholds

### Trade Execution
- **Entry**: Limit-on-Open at lastClose × 1.002 (slight premium for execution)
- **Stop Loss**: GTC stop at entry - 2×ATR14 (volatility-based protection)
- **Trailing Stops**: Daily updates to 2×ATR trailing stops for capital preservation
- **Order Management**: Cancel existing orders before new trades

### Options Overlay Strategies
- **Protective Puts**: Downside protection during high VIX regimes
- **Covered Calls**: Income generation on 100+ share positions
- **Delta-Efficient Exposure**: Deep ITM calls for capital efficiency on large accounts

### Default Universe

The bot uses these symbols by default (when universe.csv is not found):
- SPY (S&P 500 ETF)
- QQQ (NASDAQ ETF)
- AAPL (Apple)
- MSFT (Microsoft)
- NVDA (NVIDIA)

Create a `universe.csv` file in the project root with one symbol per line to customize the universe.

## 📊 Market Data Integration

The bot uses a sophisticated market data system combining Alpaca and Polygon data sources with intelligent caching and real-time streaming:

### Data Sources & Capabilities

#### Alpaca Markets (Primary Trading Data)
- **Account Data**: Real-time account information, equity, buying power, day P&L
- **Positions**: Current holdings with real-time P&L tracking and position details
- **Live Fills**: Recent trade executions, order status, and fill notifications
- **Equity/ETF Data**: Daily and minute bars for all tradeable symbols
- **Real-time Streaming**: Live quotes, bars, and trade updates via WebSocket
- **Order Management**: Trade execution, order placement, and position management
- **Rate Limiting**: 200 requests/minute with exponential backoff retry policies

#### Polygon.io (Enhanced Market Data)
- **Index Data**: SPX, VIX, DJI, NDX real-time values and historical bars
- **Options Data**: Greeks (Delta, Gamma, Theta, Vega), Implied Volatility, Open Interest
- **VIX Term Structure**: Volatility term structure for hedging and regime analysis
- **Fallback Provider**: Minute bars when Alpaca hits rate limits
- **Advanced Filtering**: Universe screening with ADV (Average Daily Volume) filters
- **Rate Limiting**: 5 requests/second with exponential backoff retry policies

### SQLite Caching System
- **Stock Bar Cache**: 1-year retention with daily/minute timeframes
- **Index Bar Cache**: Historical index data with nightly refresh
- **Compression**: Automatic compression for bars older than 30 days
- **Performance Optimization**: Bulk inserts, connection pooling, cache warming
- **Metrics Tracking**: Cache hit ratios, API call savings, performance monitoring

### Historical Data Examples
```csharp
// Daily stock bars from Alpaca (with automatic caching)
var dailyBars = await marketDataService.GetStockBarsAsync("AAPL", startDate, endDate);

// Minute bars with automatic Polygon fallback on throttling
var minuteBars = await marketDataService.GetStockMinuteBarsAsync("AAPL", startDate, endDate);

// Multiple symbols with batch processing and cache optimization
var symbols = new[] { "SPY", "QQQ", "MSFT", "NVDA", "TSLA" };
var allBars = await marketDataService.GetStockBarsAsync(symbols, startDate, endDate);

// Index data from Polygon (cached for performance)
var spxValue = await marketDataService.GetIndexValueAsync("I:SPX");
var vixBars = await marketDataService.GetIndexBarsAsync("I:VIX", startDate, endDate);

// Universe screening with advanced filters
var universe = await marketDataService.GetUniverseWithAdvFilterAsync(minAdv: 20_000_000m);
```

### Account & Portfolio Examples
```csharp
// Account information
var account = await marketDataService.GetAccountAsync();
Console.WriteLine($"Equity: {account.Equity:C}, Buying Power: {account.BuyingPower:C}");

// Current positions
var positions = await marketDataService.GetPositionsAsync();
foreach (var position in positions)
{
    Console.WriteLine($"{position.Symbol}: {position.Quantity} shares, P&L: {position.UnrealizedProfitLoss:C}");
}

// Recent fills/executions
var recentFills = await marketDataService.GetRecentFillsAsync(10);
```

### Options Data Examples
```csharp
// Get options chain for SPY with Greeks and IV
var spyOptions = await marketDataService.GetOptionsDataAsync("SPY");

// Get protective put options (30 DTE)
var protectivePuts = await marketDataService.GetProtectivePutOptionsAsync("SPY", 30);

// Get covered call options (7 DTE, OTM)
var coveredCalls = await marketDataService.GetCoveredCallOptionsAsync("SPY", currentPrice, 7);

// VIX term structure for volatility analysis
var vixTerm = await marketDataService.GetVixTermStructureAsync();

// Real-time options quotes with Greeks
var optionSymbols = new[] { "SPY241220C00500000", "SPY241220P00480000" };
var optionQuotes = await marketDataService.GetOptionsQuotesAsync(optionSymbols);
```

### Real-time Streaming Examples
```csharp
// Set up streaming service with enhanced capabilities
var streamingService = serviceProvider.GetRequiredService<IStreamingDataService>();

// Subscribe to equity events (Alpaca)
streamingService.QuoteReceived += (sender, e) =>
    Console.WriteLine($"{e.Symbol}: Bid={e.BidPrice:C} Ask={e.AskPrice:C}");

streamingService.TradeUpdated += (sender, e) =>
    Console.WriteLine($"Trade: {e.Symbol} {e.Side} {e.Quantity} @ {e.Price:C}");

// Subscribe to volatility events (Polygon)
streamingService.VixSpikeDetected += (sender, e) =>
    Console.WriteLine($"VIX SPIKE: {e.CurrentVix} (threshold: {e.Threshold})");

streamingService.OptionsQuoteReceived += (sender, e) =>
    Console.WriteLine($"Option: {e.Symbol} Delta={e.Delta:F3} IV={e.ImpliedVolatility:P2}");

// Connect to both data sources
await streamingService.ConnectAlpacaStreamAsync();
await streamingService.ConnectPolygonStreamAsync();

// Subscribe to various data streams
await streamingService.SubscribeToQuotesAsync(new[] { "SPY", "QQQ", "AAPL" });
await streamingService.SubscribeToTradeUpdatesAsync();
await streamingService.SubscribeToVixUpdatesAsync();
await streamingService.SubscribeToIndexUpdatesAsync(new[] { "I:SPX", "I:VIX" });
```

### Supported Index Symbols (Polygon)
- `I:SPX` - S&P 500 Index
- `I:VIX` - CBOE Volatility Index
- `I:DJI` - Dow Jones Industrial Average
- `I:NDX` - NASDAQ 100 Index

### Error Handling & Resilience
- **Automatic Fallback**: Polygon minute bars when Alpaca throttles
- **Retry Logic**: Exponential backoff for transient failures
- **Connection Recovery**: Automatic reconnection for streaming data
- **Graceful Degradation**: Continue operation with partial data sources

### Clock Alignment & Timestamp Handling
- **Consistent UTC**: All timestamps normalized to UTC across data sources
- **Polygon Conversion**: Milliseconds-since-epoch converted via DateTimeOffset
- **Timezone Safety**: Proper handling of market hours and daylight saving
- **Data Mixing**: Seamless integration of Alpaca and Polygon timestamps

## 📝 Logging & Monitoring

### Logging Configuration
Logs are written to multiple destinations with structured logging via Serilog:
- **Console**: Real-time colored output with log levels
- **File**: `logs/sma-trend-follower-YYYY-MM-DD.log` (daily rolling files, 30-day retention)
- **Structured Data**: JSON-formatted logs for external monitoring systems

### Log Levels
- **Information**: Trading cycle progress, signal generation, trade executions
- **Warning**: API throttling, insufficient data, minor issues
- **Error**: Trade execution failures, API errors, recoverable exceptions
- **Critical**: System failures, safety guard violations, unrecoverable errors

### Discord Notifications
Real-time notifications sent to Discord channel:
- **Trade Executions**: Symbol, action, quantity, price, P&L
- **Portfolio Updates**: Daily snapshots with equity, P&L, position count
- **VIX Alerts**: Volatility spike notifications with recommended actions
- **Options Activity**: Strategy executions and opportunities

## ⚡ Execution

### Single-Shot Execution Model
The bot uses a manual execution model optimized for scheduled deployment:

1. **Market Session Guard**: Validates trading is allowed (weekdays only)
2. **Safety Validation**: Comprehensive safety checks before trading cycle
3. **Trading Cycle Execution**: Complete strategy execution with error handling
4. **Clean Exit**: Graceful shutdown with resource cleanup

### Execution Flow
```
Program.cs → MarketSessionGuard → TradingService.ExecuteCycleAsync() → Exit
```

### Deployment Options
- **Manual**: `dotnet run --project SmaTrendFollower.Console`
- **Scheduled**: External scheduler (cron, Task Scheduler, GitHub Actions)
- **Docker**: Containerized deployment with environment variables
- **Cloud**: Azure Functions, AWS Lambda, or similar serverless platforms

## 🛡️ Safety Features

### Trading Safety Guards
- **Environment Validation**: Automatic paper/live environment detection
- **Position Limits**: Configurable maximum position sizes and daily loss limits
- **Market Hours Check**: Only trades during market sessions (weekdays)
- **Account Validation**: Minimum equity requirements and buying power checks
- **VIX Spike Protection**: Automatic position size reduction during high volatility

### Risk Controls
- **Maximum Single Trade**: Configurable per-trade value limits
- **Daily Loss Limits**: Automatic trading halt on excessive losses
- **Portfolio Concentration**: Prevents over-concentration in single positions
- **Stop-Loss Management**: Mandatory 2×ATR stops on all positions
- **Confirmation Requirements**: Optional confirmation for live trading

### Error Handling & Recovery
- **Comprehensive Exception Handling**: Graceful error recovery with logging
- **API Rate Limiting**: Built-in rate limiting with exponential backoff
- **Connection Recovery**: Automatic reconnection for streaming data
- **Data Validation**: Input validation and data integrity checks
- **Dry Run Mode**: Simulation mode for strategy testing without real trades

## 🔧 Development

### Project Structure

```
SmaTrendFollower/
├── SmaTrendFollower.Console/           # Main console application
│   ├── Services/                       # Business logic services (40+ services)
│   │   ├── Core/                       # Core trading services
│   │   ├── Enhanced/                   # Options and volatility services
│   │   ├── Data/                       # Market data and caching services
│   │   ├── Infrastructure/             # Client factories and utilities
│   │   └── Safety/                     # Safety guards and risk management
│   ├── Models/                         # Data models and primitives
│   │   ├── TradingPrimitives.cs        # Core trading data structures
│   │   ├── PolygonModels.cs            # Polygon API models
│   │   └── CacheModels.cs              # SQLite cache entities
│   ├── Extensions/                     # Extension methods for indicators
│   ├── Data/                           # Database contexts and cache models
│   ├── Examples/                       # Usage examples and demos
│   ├── Program.cs                      # Application entry point with DI setup
│   └── .env.example                    # Environment variables template
├── SmaTrendFollower.Tests/             # Comprehensive test suite (230+ tests)
│   ├── Services/                       # Service unit tests
│   └── Integration/                    # Integration tests (require API keys)
├── Documentation/                      # Additional documentation files
├── README.md                           # This file
├── universe.csv                        # Optional symbol universe file
├── *.db                               # SQLite cache databases
└── logs/                              # Application logs
```

### Adding New Features

1. **Create Service Interface**: Define contract in `Services/I{ServiceName}.cs`
2. **Implement Service**: Create implementation with proper dependency injection
3. **Register Service**: Add to DI container in `Program.cs`
4. **Add Unit Tests**: Create comprehensive tests in `SmaTrendFollower.Tests/Services/`
5. **Add Integration Tests**: Create integration tests if external APIs are involved
6. **Update Documentation**: Update README.md and add inline code documentation

### Testing Strategy

- **Unit Tests**: Test individual service components in isolation using Moq
- **Integration Tests**: Test service interactions with real or mock APIs
- **Performance Tests**: Cache performance and API rate limiting validation
- **Safety Tests**: Validate trading safety guards and risk controls

### Code Quality Standards

- **Dependency Injection**: All services use constructor injection
- **Async/Await**: All I/O operations are asynchronous
- **Error Handling**: Comprehensive exception handling with logging
- **Rate Limiting**: All API calls use rate limiting policies
- **Resource Management**: Proper disposal of clients and connections

## 📊 Performance & Metrics

### Build & Test Status
- ✅ **Build**: Successful (0 errors, minimal warnings)
- ✅ **Tests**: 230 total, 222 passed, 8 skipped (integration tests requiring API keys)
- ✅ **Test Coverage**: Comprehensive unit and integration test coverage
- ✅ **Pass Rate**: 96.5% (skipped tests require live API credentials)

### System Capabilities
- **Service Architecture**: 40+ services with complete interface implementations
- **Data Sources**: Dual Alpaca + Polygon integration with intelligent fallback
- **Caching Performance**: SQLite-based caching with compression and optimization
- **Real-time Streaming**: WebSocket connections for live market data
- **Options Strategies**: Advanced options overlay capabilities
- **Safety Systems**: Comprehensive trading safety guards and risk controls

## ⚠️ Disclaimer

**IMPORTANT**: This software is for educational and research purposes only.

- **Risk Warning**: Trading involves substantial risk of loss and is not suitable for all investors
- **No Guarantees**: Past performance does not guarantee future results
- **Testing Required**: Always test thoroughly with paper trading before using real money
- **Professional Advice**: Consult with financial professionals before live trading
- **User Responsibility**: Users are solely responsible for their trading decisions and outcomes

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please read the contributing guidelines and ensure all tests pass before submitting pull requests.

## 📞 Support

For questions, issues, or feature requests, please create an issue in the GitHub repository.
